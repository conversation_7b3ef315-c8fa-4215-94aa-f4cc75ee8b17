Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.47f1 (2ad1ed33fd3b) revision 2806253'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreSingleLanguage' Language: 'fr' Physical Memory: 16051 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker55
-projectPath
C:/Users/<USER>/ONU
-logFile
Logs/AssetImportWorker55.log
-srvPort
56204
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/ONU
C:/Users/<USER>/ONU
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [21048]  Target information:

Player connection [21048]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 1019268228 [EditorId] 1019268228 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-34E5P9N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [21048] Host joined multi-casting on [***********:54997]...
Player connection [21048] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 372.56 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.47f1 (2ad1ed33fd3b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/ONU/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 (ID=0x1f9d)
    Vendor:   NVIDIA
    VRAM:     3937 MB
    Driver:   32.0.15.7602
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56732
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 1.559240 seconds.
- Loaded All Assemblies, in 28.060 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 302 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.613 seconds
Domain Reload Profiling: 28673ms
	BeginReloadAssembly (20829ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (1266ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (1598ms)
	LoadAllAssembliesAndSetupDomain (4357ms)
		LoadAssemblies (20843ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (4339ms)
			TypeCache.Refresh (4337ms)
				TypeCache.ScanAssembly (3400ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (614ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (568ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (377ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (41ms)
			ProcessInitializeOnLoadAttributes (95ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 35.236 seconds
Refreshing native plugins compatible for Editor in 2.12 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mirror | mirror-networking.com | discord.gg/N9QVxbM
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.607 seconds
Domain Reload Profiling: 36840ms
	BeginReloadAssembly (147ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (35008ms)
		LoadAssemblies (32910ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2182ms)
			TypeCache.Refresh (2115ms)
				TypeCache.ScanAssembly (1939ms)
			BuildScriptInfoCaches (50ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1608ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (708ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (110ms)
			ProcessInitializeOnLoadAttributes (371ms)
			ProcessInitializeOnLoadMethodAttributes (206ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 2.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 219 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7357 unused Assets / (7.2 MB). Loaded Objects now: 8019.
Memory consumption went from 188.9 MB to 181.7 MB.
Total: 10.448000 ms (FindLiveObjects: 0.653800 ms CreateObjectMapping: 0.510000 ms MarkObjects: 5.597600 ms  DeleteObjects: 3.684600 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 20.165 seconds
Refreshing native plugins compatible for Editor in 18.14 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  3.904 seconds
Domain Reload Profiling: 24046ms
	BeginReloadAssembly (3719ms)
		ExecutionOrderSort (3ms)
		DisableScriptedObjects (428ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (4ms)
		CreateAndSetChildDomain (1032ms)
	RebuildCommonClasses (520ms)
	RebuildNativeTypeToScriptingClass (54ms)
	initialDomainReloadingComplete (220ms)
	LoadAllAssembliesAndSetupDomain (15628ms)
		LoadAssemblies (14474ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2509ms)
			TypeCache.Refresh (96ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (2317ms)
			ResolveRequiredComponents (45ms)
	FinalizeReload (3906ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2733ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (51ms)
			SetLoadedEditorAssemblies (19ms)
			BeforeProcessingInitializeOnLoad (325ms)
			ProcessInitializeOnLoadAttributes (1479ms)
			ProcessInitializeOnLoadMethodAttributes (823ms)
			AfterProcessingInitializeOnLoad (32ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (57ms)
Refreshing native plugins compatible for Editor in 17.24 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 43 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7355 unused Assets / (5.7 MB). Loaded Objects now: 8034.
Memory consumption went from 171.9 MB to 166.1 MB.
Total: 45.520700 ms (FindLiveObjects: 1.892600 ms CreateObjectMapping: 2.114300 ms MarkObjects: 29.198600 ms  DeleteObjects: 12.308300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.286 seconds
Refreshing native plugins compatible for Editor in 2.77 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.263 seconds
Domain Reload Profiling: 2549ms
	BeginReloadAssembly (302ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (880ms)
		LoadAssemblies (687ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (335ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (294ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1263ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (993ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (197ms)
			ProcessInitializeOnLoadAttributes (511ms)
			ProcessInitializeOnLoadMethodAttributes (249ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 4.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7358 unused Assets / (7.8 MB). Loaded Objects now: 8039.
Memory consumption went from 169.5 MB to 161.7 MB.
Total: 17.428500 ms (FindLiveObjects: 0.877700 ms CreateObjectMapping: 1.064400 ms MarkObjects: 8.851800 ms  DeleteObjects: 6.632400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'log4net.dll' with different versions detected, using 'Packages/com.unity.package-validation-suite/Lib/Editor/log4net.dll, AssemblyName=log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a' and ignoring 'Packages/com.unity.collab-proxy/Lib/Editor/log4netPlastic.dll, AssemblyName=log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a'.Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.044 seconds
Refreshing native plugins compatible for Editor in 3.20 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation. ---> System.NullReferenceException: Object reference not set to an instance of an object
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore..ctor (System.String directory) [0x0006e] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:55 
  at Unity.Multiplayer.Playmode.Workflow.Editor.ProjectDataStore.GetMain () [0x00000] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Configuration\ProjectDataStore.cs:36 
  at Unity.Multiplayer.Playmode.Workflow.Editor.WorkflowMainEditorContext..ctor (Unity.Multiplayer.Playmode.VirtualProjects.Editor.MainEditorContext mainEditorContext) [0x0002c] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Contexts\WorkflowMainEditorContext.cs:15 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow.InitializeMPPMContexts () [0x00018] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:198 
  at Unity.Multiplayer.Playmode.Workflow.Editor.VirtualProjectWorkflow+<>c.<.cctor>b__22_0 () [0x00036] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\Workflow\Editor\Workflow\VirtualProjectWorkflow.cs:85 
  at Unity.Multiplayer.Playmode.VirtualProjects.Editor.EditorContexts.SendReadyEvent () [0x00038] in .\Library\PackageCache\com.unity.multiplayer.playmode@7f4b34d911b7\VirtualProjects\Editor\Contexts\EditorContexts.cs:56 
  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <********************************>:0 
   --- End of inner exception stack trace ---
  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x00083] in <********************************>:0 
  at System.Reflection.MethodBase.Invoke (System.Object obj, System.Object[] parameters) [0x00000] in <********************************>:0 
  at UnityEditor.EditorAssemblies.ProcessInitializeOnLoadMethodAttributes () [0x00056] in <f8e1f14ef858402482f58cef2e987b5b>:0 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.220 seconds
Domain Reload Profiling: 2266ms
	BeginReloadAssembly (248ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (68ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (697ms)
		LoadAssemblies (511ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (306ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (271ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1220ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (932ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (161ms)
			ProcessInitializeOnLoadAttributes (490ms)
			ProcessInitializeOnLoadMethodAttributes (249ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 4.26 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7358 unused Assets / (7.2 MB). Loaded Objects now: 8041.
Memory consumption went from 169.5 MB to 162.3 MB.
Total: 17.327400 ms (FindLiveObjects: 1.014100 ms CreateObjectMapping: 1.093200 ms MarkObjects: 9.330600 ms  DeleteObjects: 5.887600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1868992.246224 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_01B.prefab
  artifactKey: Guid(083a7f54e13bc9d4fab2545a00e71678) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_01B.prefab using Guid(083a7f54e13bc9d4fab2545a00e71678) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e6414cf434852e132c6a2d962ed2f614') in 1.281374 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_02B.prefab
  artifactKey: Guid(11124ae3c6daecc4aa835ba101dd77bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_02B.prefab using Guid(11124ae3c6daecc4aa835ba101dd77bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1d0aa6e7173e75527ee61c6e34373b28') in 0.0985128 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelf_Box_01A.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelf_Box_01A.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fcc81921a485cfdab4e256bbd7bb6fad') in 0.0592545 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelf_Box_01C.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelf_Box_01C.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f9e9c548fba07d067561599d6674d087') in 0.0796453 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelf_Box_01B.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelf_Box_01B.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '877c9c3cc5903b91668e92dd55e9580f') in 0.0612475 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Standing_Lamp_01A.prefab
  artifactKey: Guid(28f330532e22064438a11c101dd3fb15) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Standing_Lamp_01A.prefab using Guid(28f330532e22064438a11c101dd3fb15) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6a6b3739630071a3aed2b6eb0de216ba') in 0.0899182 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_03A.prefab
  artifactKey: Guid(550a3ca4f8e41a9489af5b64e9a22162) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_03A.prefab using Guid(550a3ca4f8e41a9489af5b64e9a22162) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '859786215c927b8e45937796bc6eb261') in 0.0553029 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_04C.prefab
  artifactKey: Guid(f7113934a05059543975891f56c0eb97) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_04C.prefab using Guid(f7113934a05059543975891f56c0eb97) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c29d5a93804774c9df94b525160d9c23') in 0.0414369 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelves_03A.prefab
  artifactKey: Guid(6b093c380c43f8f409b9965f9a06a2e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelves_03A.prefab using Guid(6b093c380c43f8f409b9965f9a06a2e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c7f0bd56d07bb0061ab610708bda5db8') in 0.0646296 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_06A.prefab
  artifactKey: Guid(b7813676c823c014388bca3ba0b6ada4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_06A.prefab using Guid(b7813676c823c014388bca3ba0b6ada4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '638f0e70a4984cf2d5a3cc8ca8155878') in 0.0542816 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelf_Box_02A.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Shelf_Box_02A.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '347c58b8394267b490ab001d5952384a') in 0.0626133 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_02C.prefab
  artifactKey: Guid(8f30ae520f6eea84bb8678cc4ba3c22a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_02C.prefab using Guid(8f30ae520f6eea84bb8678cc4ba3c22a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'be0fff27af06015d97543d6d48bc2343') in 0.0614742 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_05A.prefab
  artifactKey: Guid(b0d747380b605464192d7d8a245309f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_05A.prefab using Guid(b0d747380b605464192d7d8a245309f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ea852d23663e67df7214df1172bc1561') in 0.0646176 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_04A.prefab
  artifactKey: Guid(197096e2bdb80bb43b93c9f6dd2d911b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_04A.prefab using Guid(197096e2bdb80bb43b93c9f6dd2d911b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6bf82696e3dd7188075f727d1ea730ba') in 0.0442392 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_01C.prefab
  artifactKey: Guid(c714e46713b40d64b9c4862e1d26ab51) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_01C.prefab using Guid(c714e46713b40d64b9c4862e1d26ab51) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a3cf2f7129fbbd4a8e8e5903bbe52a8f') in 0.0435461 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_02A.prefab
  artifactKey: Guid(43da608b2c105f5499e65692622e7f8b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Toon Suburban Pack/Prefabs/Interiors/House Props/TSP_Rug_02A.prefab using Guid(43da608b2c105f5499e65692622e7f8b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a865d6b81a4ff4fa377cb2a9c2f841d4') in 0.074427 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0