using UnityEngine;

public class SpeedQuiz : Quest
{
    [Header("Speed Quiz Settings")]
    public float timeLimit = 30f;
    public int questionsCount = 10;

    // Start is called before the first frame update
    void Start()
    {

    }

    // Update is called once per frame
    void Update()
    {

    }

    public void CompleteTask()
    {
        Tasks task = transform.parent.GetComponent<Tasks>();
        if (task != null)
        {
            task.CompleteTask();
        }
    }
}