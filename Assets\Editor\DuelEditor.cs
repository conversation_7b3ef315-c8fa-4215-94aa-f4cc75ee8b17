using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(Duel))]
public class DuelEditor : QuestEditor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
    }
    
    
    protected override void DrawChildClassContent()
    {
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Duel Quest Settings", EditorStyles.boldLabel);
        
        Duel duel = (Duel)target;
        
        EditorGUILayout.Space();
        
        if (GUILayout.But<PERSON>("Complete Duel Task"))
        {
            if (Application.isPlaying)
            {
                duel.CompleteTask();
            }
            else
            {
                EditorUtility.DisplayDialog("Duel Task", "This action can only be performed during play mode.", "OK");
            }
        }
        
        EditorGUILayout.Space();
        EditorGUILayout.HelpBox("This is a Duel quest. Use the 'Complete Duel Task' button during play mode to complete the task.", MessageType.Info);
    }
}
