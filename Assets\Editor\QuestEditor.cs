using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(Quest), true)]
public class QuestEditor : Editor
{
    public bool showDetails = false;

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        EditorGUILayout.Space();

        if(GUILayout.Button("Add NPC"))
        {
            Quest quest = (Quest)target;
            quest.instanciateNPC();
        }
        DrawChildClassContent();
    }


    protected virtual void DrawChildClassContent()
    {
        // Override in child editors for specific functionality
    }
}