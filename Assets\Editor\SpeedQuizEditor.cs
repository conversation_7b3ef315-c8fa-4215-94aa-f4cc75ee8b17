using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(SpeedQuiz))]
public class SpeedQuizEditor : QuestEditor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
    }
    
    protected override void DrawQuestButtons()
    {
        base.DrawQuestButtons();
        
        if (GUILayout.Button("Create Speed Quiz Quest"))
        {
            GameObject go = new GameObject("Speed Quiz Quest");
            go.AddComponent<SpeedQuiz>();
            Selection.activeGameObject = go;
        }
    }
    
    protected override void DrawChildClassContent()
    {
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Speed Quiz Quest Settings", EditorStyles.boldLabel);
        
        SpeedQuiz speedQuiz = (SpeedQuiz)target;
        
        EditorGUILayout.Space();
        
        // Show Speed Quiz specific properties
        EditorGUILayout.LabelField("Configuration", EditorStyles.boldLabel);
        EditorGUILayout.LabelField($"Time Limit: {speedQuiz.timeLimit} seconds");
        EditorGUILayout.LabelField($"Questions Count: {speedQuiz.questionsCount}");
        
        EditorGUILayout.Space();
        
        if (GUILayout.Button("Complete Speed Quiz Task"))
        {
            if (Application.isPlaying)
            {
                speedQuiz.CompleteTask();
            }
            else
            {
                EditorUtility.DisplayDialog("Speed Quiz Task", "This action can only be performed during play mode.", "OK");
            }
        }
        
        EditorGUILayout.Space();
        EditorGUILayout.HelpBox("This is a Speed Quiz quest with time constraints. Configure the time limit and questions count in the inspector above.", MessageType.Info);
    }
}
