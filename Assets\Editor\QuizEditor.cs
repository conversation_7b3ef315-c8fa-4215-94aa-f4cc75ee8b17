using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(Quiz))]
public class QuizEditor : QuestEditor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
    }
    
    protected override void DrawQuestButtons()
    {
        base.DrawQuestButtons();
        
        if (GUILayout.Button("Create Quiz Quest"))
        {
            GameObject go = new GameObject("Quiz Quest");
            go.AddComponent<Quiz>();
            Selection.activeGameObject = go;
        }
    }
    
    protected override void DrawChildClassContent()
    {
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Quiz Quest Settings", EditorStyles.boldLabel);
        
        Quiz quiz = (Quiz)target;
        
        EditorGUILayout.Space();
        
        if (GUILayout.Button("Complete Quiz Task"))
        {
            if (Application.isPlaying)
            {
                quiz.CompleteTask();
            }
            else
            {
                EditorUtility.DisplayDialog("Quiz Task", "This action can only be performed during play mode.", "OK");
            }
        }
        
        EditorGUILayout.Space();
        EditorGUILayout.HelpBox("This is a Quiz quest. Use the 'Complete Quiz Task' button during play mode to complete the task.", MessageType.Info);
    }
}
