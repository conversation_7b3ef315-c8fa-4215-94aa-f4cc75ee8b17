# Quest Editor System

This document explains the custom editor system for Quest classes and their child classes.

## Overview

The Quest editor system provides a modular approach to creating custom Unity editors for Quest-based classes. All Quest child classes now inherit the same custom editor functionality while allowing for specific customizations.

## Architecture

### Base Editor: QuestEditor
- **File**: `Assets/Editor/QuestEditor.cs`
- **Target**: `Quest` class and all its child classes (using `[CustomEditor(typeof(Quest), true)]`)
- **Features**:
  - Base Quest functionality
  - "Create Quest" button
  - Extensible virtual methods for child classes

### Child Editors

#### DuelEditor
- **File**: `Assets/Editor/DuelEditor.cs`
- **Target**: `Duel` class
- **Features**:
  - Inherits all QuestEditor functionality
  - "Create Duel Quest" button
  - "Complete Duel Task" button (play mode only)
  - Duel-specific help information

#### QuizEditor
- **File**: `Assets/Editor/QuizEditor.cs`
- **Target**: `Quiz` class
- **Features**:
  - Inherits all QuestEditor functionality
  - "Create Quiz Quest" button
  - "Complete Quiz Task" button (play mode only)
  - Quiz-specific help information

#### SpeedQuizEditor
- **File**: `Assets/Editor/SpeedQuizEditor.cs`
- **Target**: `SpeedQuiz` class
- **Features**:
  - Inherits all QuestEditor functionality
  - "Create Speed Quiz Quest" button
  - "Complete Speed Quiz Task" button (play mode only)
  - Displays time limit and questions count configuration
  - Speed Quiz-specific help information

## Quest Class Hierarchy

```
Quest (MonoBehaviour)
├── Duel
├── Quiz
└── SpeedQuiz
```

All child classes now inherit from `Quest` and include:
- Access to NPC instantiation functionality from the base Quest class
- `CompleteTask()` method that integrates with the Tasks system
- Custom editor support

## Adding New Quest Types

To add a new Quest type:

1. **Create the Quest Class**:
   ```csharp
   public class MyNewQuest : Quest
   {
       // Add your specific functionality here
       
       public void CompleteTask()
       {
           Tasks task = transform.parent.GetComponent<Tasks>();
           if (task != null)
           {
               task.CompleteTask();
           }
       }
   }
   ```

2. **Create the Custom Editor**:
   ```csharp
   [CustomEditor(typeof(MyNewQuest))]
   public class MyNewQuestEditor : QuestEditor
   {
       protected override void DrawQuestButtons()
       {
           base.DrawQuestButtons();
           
           if (GUILayout.Button("Create My New Quest"))
           {
               GameObject go = new GameObject("My New Quest");
               go.AddComponent<MyNewQuest>();
               Selection.activeGameObject = go;
           }
       }
       
       protected override void DrawChildClassContent()
       {
           // Add your custom editor content here
       }
   }
   ```

3. **Add to TaskType Enum** (if needed):
   Add your new quest type to the `TaskType` enum in `Assets/Scripts/Tasks.cs`

## Benefits

- **Consistency**: All Quest editors share the same base functionality
- **Modularity**: Easy to add new Quest types without duplicating code
- **Extensibility**: Child editors can override specific methods to add custom functionality
- **Maintainability**: Changes to base Quest editor functionality automatically apply to all child editors

## Usage

1. Select any GameObject with a Quest component (or child class) in the Inspector
2. The appropriate custom editor will be displayed based on the component type
3. Use the provided buttons to create new quest objects or complete tasks during play mode
4. Each editor provides helpful information specific to that quest type
